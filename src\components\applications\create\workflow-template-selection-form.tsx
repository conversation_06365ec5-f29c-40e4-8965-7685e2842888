"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Workflow, ArrowLeft } from "lucide-react";

const workflowTemplateSelectionSchema = z.object({
  workflowTemplateId: z.string().min(1, "Please select a workflow template"),
});

type FormData = z.infer<typeof workflowTemplateSelectionSchema>;

interface WorkflowTemplateSelectionFormProps {
  onNext: (data: { workflowTemplateId: string }) => void;
  onBack: () => void;
  selectedProductId?: string; // Add selected product ID for filtering
  initialData?: {
    workflowTemplateId?: string;
  };
}

interface IWorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  serviceId?: string; // Immigration package ID
  workflowTemplate: Array<{
    stageName: string;
    stageOrder: number;
  }>;
}

export const WorkflowTemplateSelectionForm: React.FC<
  WorkflowTemplateSelectionFormProps
> = ({ onNext, onBack, selectedProductId, initialData }) => {
  const [templates, setTemplates] = useState<IWorkflowTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<
    IWorkflowTemplate[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [loadingTemplates, setLoadingTemplates] = useState(true);
  const [selectedTemplate, setSelectedTemplate] =
    useState<IWorkflowTemplate | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(workflowTemplateSelectionSchema),
    defaultValues: {
      workflowTemplateId: initialData?.workflowTemplateId || "",
    },
  });

  const selectedTemplateId = form.watch("workflowTemplateId");

  useEffect(() => {
    let isMounted = true;

    const fetchTemplates = async () => {
      try {
        setLoadingTemplates(true);
        const response = await fetch("/api/workflow-templates?isActive=true");
        if (response.ok) {
          const data = await response.json();
          if (isMounted) {
            setTemplates(data.data || data || []);
          }
        } else {
          throw new Error("Failed to fetch workflow templates");
        }
      } catch (error) {
        console.error("Error fetching workflow templates:", error);
      } finally {
        if (isMounted) {
          setLoadingTemplates(false);
        }
      }
    };

    fetchTemplates();

    return () => {
      isMounted = false;
    };
  }, []);

  // Filter templates based on selected product
  useEffect(() => {
    if (selectedProductId && templates.length > 0) {
      const filtered = templates.filter(
        (template) => template.serviceId === selectedProductId
      );
      setFilteredTemplates(filtered);
    } else {
      setFilteredTemplates(templates);
    }
  }, [selectedProductId, templates]);

  useEffect(() => {
    if (selectedTemplateId) {
      const template = filteredTemplates.find(
        (t) => t.id === selectedTemplateId
      );
      setSelectedTemplate(template || null);
    } else {
      setSelectedTemplate(null);
    }
  }, [selectedTemplateId, filteredTemplates]);

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    try {
      onNext({
        workflowTemplateId: data.workflowTemplateId,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Workflow className="h-5 w-5" />
          Workflow Template Selection
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="workflowTemplateId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Select Workflow Template{" "}
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={loadingTemplates}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            loadingTemplates
                              ? "Loading templates..."
                              : "Select a template"
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {filteredTemplates.length > 0 ? (
                        filteredTemplates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-templates" disabled>
                          No templates available for selected product
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedTemplate && (
              <div className="p-4 bg-primary/5 rounded-lg border">
                <h4 className="font-medium mb-2">Selected Template Details</h4>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">Name:</span>{" "}
                    {selectedTemplate.name}
                  </div>
                  {selectedTemplate.description && (
                    <div>
                      <span className="font-medium">Description:</span>{" "}
                      {selectedTemplate.description}
                    </div>
                  )}
                  <div>
                    <span className="font-medium">Stages:</span>{" "}
                    {selectedTemplate.workflowTemplate?.length || 0}
                  </div>
                  {selectedTemplate.workflowTemplate &&
                    selectedTemplate.workflowTemplate.length > 0 && (
                      <div>
                        <span className="font-medium">Stage Names:</span>
                        <ul className="list-disc list-inside ml-4 mt-1">
                          {selectedTemplate.workflowTemplate
                            .sort((a, b) => a.stageOrder - b.stageOrder)
                            .map((stage, index) => (
                              <li key={index}>{stage.stageName}</li>
                            ))}
                        </ul>
                      </div>
                    )}
                </div>
              </div>
            )}

            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
                className="flex-1"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button
                type="submit"
                disabled={loading || !selectedTemplateId}
                className="flex-1"
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Continue to Payment
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

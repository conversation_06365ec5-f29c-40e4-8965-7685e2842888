"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, UserCheck, ArrowLeft } from "lucide-react";
import { clientFetch } from "@/lib/client-fetch";

const agentAssignmentSchema = z.object({
  assignedAgentId: z.string().optional(),
  priorityLevel: z.enum(["Low", "Medium", "High"]),
  notes: z.string().optional(),
});

type FormData = z.infer<typeof agentAssignmentSchema>;

interface AgentAssignmentFormProps {
  onNext: (data: {
    assignedAgentId?: string;
    priorityLevel: string;
    notes?: string;
  }) => void;
  onBack: () => void;
  userRole?: "admin" | "agent" | "user";
  currentUserId?: string;
  initialData?: {
    assignedAgentId?: string;
    priorityLevel?: string;
    notes?: string;
  };
}

interface IAgent {
  id: string;
  name: string;
  email: string;
  status: "Active" | "Inactive" | "Blocked";
}

export const AgentAssignmentForm: React.FC<AgentAssignmentFormProps> = ({
  onNext,
  onBack,
  userRole = "admin",
  currentUserId,
  initialData,
}) => {
  const [agents, setAgents] = useState<IAgent[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingAgents, setLoadingAgents] = useState(true);

  const form = useForm<FormData>({
    resolver: zodResolver(agentAssignmentSchema),
    defaultValues: {
      assignedAgentId: initialData?.assignedAgentId || "unassigned",
      priorityLevel:
        (initialData?.priorityLevel as "Low" | "Medium" | "High") || "Medium",
      notes: initialData?.notes || "",
    },
  });

  useEffect(() => {
    const fetchAgents = async () => {
      try {
        setLoadingAgents(true);
        const response = await clientFetch("/api/agents?status=Active");
        if (response.ok) {
          const data = await response.json();
          let agentList = data.data || data || [];

          // Filter agents based on user role
          if (userRole === "agent" && currentUserId) {
            agentList = agentList.filter(
              (agent: IAgent) => agent.id === currentUserId
            );
          }

          setAgents(agentList);
        } else {
          throw new Error("Failed to fetch agents");
        }
      } catch (error) {
        console.error("Error fetching agents:", error);
      } finally {
        setLoadingAgents(false);
      }
    };

    fetchAgents();
  }, [userRole, currentUserId]);

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    try {
      onNext({
        assignedAgentId:
          data.assignedAgentId === "unassigned"
            ? undefined
            : data.assignedAgentId || undefined,
        priorityLevel: data.priorityLevel,
        notes: data.notes || undefined,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserCheck className="h-5 w-5" />
          Agent Assignment & Final Details
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="assignedAgentId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Assign Agent {userRole === "admin" ? "(Optional)" : ""}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={loadingAgents}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            loadingAgents
                              ? "Loading agents..."
                              : userRole === "admin"
                                ? "Select an agent (optional)"
                                : "Select yourself as agent"
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {userRole === "admin" && (
                        <SelectItem value="unassigned">Unassigned</SelectItem>
                      )}
                      {agents.map((agent) => (
                        <SelectItem key={agent.id} value={agent.id}>
                          {agent.name} ({agent.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                  {userRole === "agent" &&
                    agents.length === 0 &&
                    !loadingAgents && (
                      <p className="text-sm text-muted-foreground">
                        No active agent found. Please contact an administrator.
                      </p>
                    )}
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="priorityLevel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Priority Level</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select priority level" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Low">Low</SelectItem>
                      <SelectItem value="Medium">Medium</SelectItem>
                      <SelectItem value="High">High</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any additional notes or special instructions for this application..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="p-4 bg-primary/5 rounded-lg border">
              <h4 className="font-medium mb-2">Application Summary</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Priority Level:</span>
                  <span
                    className={`font-medium ${
                      form.watch("priorityLevel") === "High"
                        ? "text-red-600"
                        : form.watch("priorityLevel") === "Medium"
                          ? "text-yellow-600"
                          : "text-green-600"
                    }`}
                  >
                    {form.watch("priorityLevel")}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Assigned Agent:</span>
                  <span>
                    {form.watch("assignedAgentId")
                      ? agents.find(
                          (a) => a.id === form.watch("assignedAgentId")
                        )?.name || "Selected"
                      : "Unassigned"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Initial Status:</span>
                  <span>Pending</span>
                </div>
                <div className="flex justify-between">
                  <span>Initial Step:</span>
                  <span>1</span>
                </div>
              </div>
            </div>

            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
                className="flex-1"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button type="submit" disabled={loading} className="flex-1">
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create Application
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Workflow,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  CheckCircle2,
  Clock,
  List,
  Hash,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Validation schema for workflow selection
const workflowSelectionSchema = z.object({
  workflowTemplateId: z.string().min(1, "Please select a workflow template"),
});

type FormData = z.infer<typeof workflowSelectionSchema>;

interface WorkflowSelectionFormProps {
  onNext: (data: { workflowTemplateId: string }) => void;
  onBack: () => void;
  immigrationProductId: string;
  initialData?: { workflowTemplateId?: string };
}

/**
 * Interface representing a single workflow stage within a template
 * Contains all the configuration for a specific stage in the workflow process
 */
interface WorkflowStage {
  stageName: string;
  stageOrder: number;
  documentsRequired: boolean;
  documents: any[];
  customFormRequired: boolean;
  customForm: any[];
  showToClient: boolean;
}

/**
 * Interface representing a complete workflow template
 * Enhanced to include the workflowTemplate array for stage details display
 */
interface WorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  serviceType?: string;
  serviceId?: string;
  workflowTemplate: WorkflowStage[];
}

interface WorkflowTemplatesResponse {
  data: WorkflowTemplate[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export const WorkflowSelectionForm: React.FC<WorkflowSelectionFormProps> = ({
  onNext,
  onBack,
  immigrationProductId,
  initialData,
}) => {
  const { data: session } = useSession();
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    useState<WorkflowTemplate | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(workflowSelectionSchema),
    defaultValues: {
      workflowTemplateId: initialData?.workflowTemplateId || "",
    },
  });

  // Update selected template when form value changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "workflowTemplateId") {
        const templateId = value.workflowTemplateId;
        if (templateId) {
          const template = templates.find((t) => t.id === templateId);
          setSelectedTemplate(template || null);
        } else {
          setSelectedTemplate(null);
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form, templates]);

  // Fetch workflow templates filtered by immigration product ID
  useEffect(() => {
    const fetchWorkflowTemplates = async () => {
      if (!session?.backendTokens?.accessToken || !immigrationProductId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch workflow templates filtered by serviceId (immigration product ID)
        const queryParams = new URLSearchParams({
          serviceId: immigrationProductId,
          isActive: "true",
          page: "1",
          limit: "50", // Get up to 50 templates for the service
        });

        const response = await fetch(`/api/workflow-templates?${queryParams}`, {
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          if (response.status === 401) {
            throw new Error("Authentication failed. Please sign in again.");
          }
          throw new Error(
            `Failed to fetch workflow templates: ${response.status}`
          );
        }

        const data: WorkflowTemplatesResponse = await response.json();

        if (data.data && Array.isArray(data.data)) {
          setTemplates(data.data);

          // If no templates found for this service, show appropriate message
          if (data.data.length === 0) {
            setError(
              "No workflow templates are available for the selected Product. Please create a workflow template or contact admin."
            );
          }
        } else {
          setError("Invalid response format from server.");
        }
      } catch (err) {
        console.error("Error fetching workflow templates:", err);
        setError(
          err instanceof Error
            ? err.message
            : "Failed to load workflow templates. Please try again."
        );
      } finally {
        setLoading(false);
      }
    };

    fetchWorkflowTemplates();
  }, [session, immigrationProductId]);

  const onSubmit = async (data: FormData) => {
    setSubmitting(true);
    try {
      // Validate that the selected template exists in our list
      const selectedTemplate = templates.find(
        (t) => t.id === data.workflowTemplateId
      );
      if (!selectedTemplate) {
        throw new Error("Selected workflow template is no longer available.");
      }

      onNext({
        workflowTemplateId: data.workflowTemplateId,
      });
    } catch (err) {
      console.error("Error submitting workflow selection:", err);
      setError(
        err instanceof Error
          ? err.message
          : "Failed to proceed. Please try again."
      );
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Workflow className="h-5 w-5" />
          Select Workflow Template
        </CardTitle>
        <p className="text-sm text-gray-600 mt-2">
          Choose a workflow template that defines the process steps for this
          immigration application. Templates are customized for your selected
          service and will guide the application through completion.
        </p>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="relative">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <Workflow className="h-8 w-8 text-blue-600" />
              </div>
              <Loader2 className="h-6 w-6 animate-spin absolute -top-1 -right-1 text-blue-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Loading Workflow Templates
            </h3>
            <p className="text-sm text-gray-600">
              Fetching available templates for your selected service...
            </p>
          </div>
        ) : error ? (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : templates.length === 0 ? (
          <div className="text-center py-8">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Workflow className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Workflow Templates Available
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              No workflow templates are configured for the selected immigration
              service.
            </p>
            <Alert className="text-left">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please contact your administrator to set up workflow templates
                for this service, or go back and select a different service.
              </AlertDescription>
            </Alert>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="workflowTemplateId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Available Workflow Templates</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a workflow template" />
                        </SelectTrigger>
                        <SelectContent>
                          {templates.map((template) => (
                            <SelectItem key={template.id} value={template.id}>
                              <div className="flex items-center gap-2">
                                <span>{template.name}</span>
                                <Badge
                                  variant={
                                    template.isActive ? "default" : "secondary"
                                  }
                                  className="text-xs"
                                >
                                  <CheckCircle2 className="w-3 h-3 mr-1" />
                                  {template.isActive ? "Active" : "Inactive"}
                                </Badge>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Template Details Display - Shows comprehensive information about selected template */}
              {selectedTemplate && (
                <div className="mt-6 p-4 border rounded-lg bg-gray-50">
                  <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
                    <Workflow className="h-5 w-5" />
                    Template Details
                  </h3>

                  {selectedTemplate.description && (
                    <p className="text-sm text-gray-600 mb-4">
                      {selectedTemplate.description}
                    </p>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Total Stages Count */}
                    <div className="flex items-center gap-2 p-3 bg-white rounded-md border">
                      <Hash className="h-4 w-4 text-blue-600" />
                      <div>
                        <p className="text-sm font-medium">Total Stages</p>
                        <p className="text-lg font-bold text-blue-600">
                          {selectedTemplate.workflowTemplate?.length || 0}
                        </p>
                      </div>
                    </div>

                    {/* Stage Names List */}
                    <div className="flex items-start gap-2 p-3 bg-white rounded-md border">
                      <List className="h-4 w-4 text-green-600 mt-1" />
                      <div className="flex-1">
                        <p className="text-sm font-medium mb-2">Stage Names</p>
                        {selectedTemplate.workflowTemplate &&
                        selectedTemplate.workflowTemplate.length > 0 ? (
                          <div className="space-y-1">
                            {selectedTemplate.workflowTemplate
                              .sort((a, b) => a.stageOrder - b.stageOrder)
                              .map((stage, index) => (
                                <div
                                  key={index}
                                  className="flex items-center gap-2"
                                >
                                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                    {stage.stageOrder}
                                  </span>
                                  <span className="text-sm text-gray-700">
                                    {stage.stageName}
                                  </span>
                                </div>
                              ))}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500">
                            No stages defined
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 flex items-center text-xs text-gray-500">
                    <Clock className="w-3 h-3 mr-1" />
                    <span>Service-specific workflow template</span>
                  </div>
                </div>
              )}

              <div className="flex justify-between pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onBack}
                  disabled={submitting}
                >
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
                <Button
                  type="submit"
                  disabled={submitting || templates.length === 0}
                >
                  {submitting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      Next
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
};
